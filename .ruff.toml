extend-include = ["*.ipynb"]
preview = true
line-length = 100
indent-width = 2

[format]
preview = true
quote-style = "single"
indent-style = "space"
skip-magic-trailing-comma = true
docstring-code-format = true

[lint]
ignore = [
  "RUF012",
  "ANN",    # Mypy is better at this
  "E722",
]
select = [
  "F",
  "G",     # flake8-logging-format
  "PERF",  # perflint
  "RUF",   # Ruff-specific rules
  "W6",
  "E71",
  "E72",
  "E112",
  "E113",
  "E203",
  "E272",
  "E702",
  "E703",
  "E731",
  "W191",
  "W291",
  "W293",
  "UP039", # unnecessary-class-parentheses
]

[lint.pydocstyle]
convention = "google"
