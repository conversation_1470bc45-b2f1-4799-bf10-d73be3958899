name: 🚀 Feature Request
description: Submit a proposal/request for new OpenLLM features.
title: 'feat: '
labels: ['']
body:
  - type: textarea
    id: feature-request
    validations:
      required: true
    attributes:
      label: Feature request
      description: |
        A clear and concise description of the feature request.
      placeholder: |
        I would like it if...
  - type: textarea
    id: motivation
    validations:
      required: false
    attributes:
      label: Motivation
      description: |
        Please outline the motivation for this feature request. Is your feature request related to a problem? e.g., I'm always frustrated when [...].
        If this is related to another issue, please link here too.
        If you have a current workaround, please also provide it here.
      placeholder: |
        This feature would solve ...
  - type: textarea
    id: other
    attributes:
      label: Other
      description: |
        Is there any way that you could help, e.g. by submitting a PR?
      placeholder: |
        I would love to contribute ...
