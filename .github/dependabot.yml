version: 2
updates:
  - package-ecosystem: github-actions
    directory: '/'
    schedule:
      interval: 'weekly'
      day: 'monday'
      time: '09:00'
    groups:
      actions-dependencies:
        applies-to: 'version-updates'
        patterns:
          - '*'
  - package-ecosystem: pip
    directory: '/'
    schedule:
      interval: 'weekly'
    open-pull-requests-limit: 5
    groups:
      production-dependencies:
        applies-to: 'version-updates'
        patterns:
          - '*'
