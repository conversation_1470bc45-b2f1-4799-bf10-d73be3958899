name: release
on:
  push:
    tags:
      - "*"
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # ratchet:actions/checkout@v4
      - name: Setup Python
        uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # ratchet:actions/setup-python@v5
        with:
          python-version-file: .python-version-default
      - name: Build
        run: pipx run build
      - name: Upload artifacts
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # ratchet:actions/upload-artifact@v4
        with:
          name: python-artefacts-openllm
          path: dist/*
          if-no-files-found: error
  release:
    if: github.repository_owner == 'bentoml'
    needs:
      - build
    runs-on: ubuntu-latest
    name: Release
    permissions:
      id-token: write
      contents: write
    steps:
      - name: Download Python artifacts
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # ratchet:actions/download-artifact@v4
        with:
          pattern: python-artefacts-*
          merge-multiple: true
          path: dist
      - name: dry ls
        run: ls -rthlaR
      - name: Publish to PyPI
        uses: pypa/gh-action-pypi-publish@76f52bc884231f62b9a034ebfe128415bbaabdfc # ratchet:pypa/gh-action-pypi-publish@release/v1
        with:
          print-hash: true
      - name: Create release
        uses: softprops/action-gh-release@da05d552573ad5aba039eaac05058a918a7bf631 # ratchet:softprops/action-gh-release@v2
        with:
          # Use GH feature to populate the changelog automatically
          generate_release_notes: true
          fail_on_unmatched_files: true
          files: |-
            dist/*
