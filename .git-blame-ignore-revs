# You can use this file with 'git config blame.ignoreRevsFile .git-blame-ignore-revs'
# 07/31/2023: Style guidelines
8c2867d26dfff8a4cf33bc59d5a8dee159f3256a
# 08/22/2023: Running yapf with guidelines
1488fbb167a0ae5b0770f33f50a7ee7f7b2223c9
eddbc063743b198d72c21bd7dced59dbd949b9f1
# 08/23/2023: Synchronize style guidelines
787ce1b3b63ecbacde371550f46fa7429f3e4db2
# 08/25/2023: Consistency between yapf and ruff
46c890480640294c3f34706d595559c7ea97dac5
# 08/26/2023: Add one blank space between top level definition to similar to Google Style Guide
806a663e4aa2b174969241f6e310e05762e233f0
# 08/30/2023: Update to google style
b545ad2ad1e3acbb69f6578d8a5ee03613867505
# 09/01/2023: ignore new line split on comma-separated item
7d893e6cd217ddfe845210503c8f2cf1667d16b6
# 11/09/2023: running ruff format preview
ac377fe490bd886cf76c3855e6a2a50fc0e03b51
# 11/26/2023: reduce line overhead
69aae34cf4e6995edf2e2dc1a669fc4bdecf959a
# 11/28/2023: compact
d04309188b8fccec6a3ff36a893099806f560551
# 12/14/2023: using ruff to 150 LL
c8c9663d06e49da327ed53a22bea79f78d808aa9
# 03/15/2024: ignore new ruff formatter
727361ced761c82351ff539fcafa7af62fb5e2f0
