ci:
  autoupdate_schedule: weekly
  autofix_commit_msg: "ci: auto fixes from pre-commit.ci\n\nFor more information, see https://pre-commit.ci"
  autoupdate_commit_msg: "ci: pre-commit autoupdate [pre-commit.ci]"
  autofix_prs: true
default_language_version:
  python: python3.11 # NOTE: sync with .python-version-default
repos:
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: "v0.11.13"
    hooks:
      - id: ruff
        alias: r
        verbose: true
        args: [--exit-non-zero-on-fix, --show-fixes, --fix]
        types_or: [python, pyi, jupyter]
      - id: ruff-format
        alias: rf
        verbose: true
        types_or: [python, pyi, jupyter]
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: "v1.16.0"
    hooks:
      - id: mypy
        args: [--strict, --ignore-missing-imports]
        additional_dependencies:
          [pydantic, types-pyyaml, types-tabulate, types-psutil, typer]
  - repo: https://github.com/editorconfig-checker/editorconfig-checker.python
    rev: "3.2.1"
    hooks:
      - id: editorconfig-checker
        verbose: true
        alias: ec
        types_or: [python]
  - repo: meta
    hooks:
      - id: check-hooks-apply
      - id: check-useless-excludes
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
        verbose: true
      - id: end-of-file-fixer
        verbose: true
      - id: check-yaml
        args: ["--unsafe"]
      - id: check-toml
      - id: check-docstring-first
      - id: check-added-large-files
      - id: debug-statements
      - id: check-merge-conflict
